destilink:
  fw:
    locking:
      redis:
        enabled: true
        lease-time: PT60S
        retry-interval: PT0.1S
        state-key-expiration: PT5M
        pub-sub-wait-timeout: PT5S
        watchdog:
          interval: PT10S
          factor: 0.3
          core-pool-size: 2
          thread-name-prefix: "redis-lock-watchdog-"
          shutdown-await-termination: PT30S
        lock-owner-id-validation-regex: "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"
        max-lock-name-length: 255
        max-bucket-name-length: 100
        max-scope-length: 100