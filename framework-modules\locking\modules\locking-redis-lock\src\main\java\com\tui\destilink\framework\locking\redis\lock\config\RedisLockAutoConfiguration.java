package com.tui.destilink.framework.locking.redis.lock.config;

import com.tui.destilink.framework.locking.redis.lock.service.*;
import com.tui.destilink.framework.locking.redis.lock.service.impl.*;
import com.tui.destilink.framework.redis.core.RedisCoreAutoConfiguration;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Auto-configuration for Redis-based distributed locking.
 * This class sets up the necessary beans for Redis lock operations,
 * including script loading, lock operations, error handling, and monitoring.
 */
@AutoConfiguration
@EnableConfigurationProperties(RedisLockProperties.class)
@AutoConfigureAfter(RedisCoreAutoConfiguration.class)
@ConditionalOnClass(ClusterCommandExecutor.class)
// ConditionalOnBean(RedisConnectionFactory.class)
@ConditionalOnProperty(prefix = "destilink.fw.locking.redis", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RedisLockAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ScriptLoader scriptLoader() {
        return new ScriptLoaderImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockOperations redisLockOperations(ClusterCommandExecutor executor, ScriptLoader loader,
                                                   RedisLockProperties properties, ExecutorService virtualThreadExecutor) {
        return new RedisLockOperationsImpl(executor, loader, properties, virtualThreadExecutor);
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultLockOwnerSupplier defaultLockOwnerSupplier() {
        return new DefaultLockOwnerSupplier();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockErrorHandler redisLockErrorHandler() {
        return new RedisLockErrorHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public LockMonitor lockMonitor(ObjectProvider<MeterRegistry> meterRegistry) {
        return new DefaultLockMonitor();
    }

    @Bean(name = "redisLockMessageExecutor")
    @ConditionalOnMissingBean(name = "redisLockMessageExecutor")
    public Executor redisLockMessageExecutor() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(Runtime.getRuntime().availableProcessors());
        scheduler.setThreadNamePrefix("redis-lock-msg-");
        scheduler.setDaemon(true);
        scheduler.initialize();
        return scheduler;
    }

    @Bean
    @ConditionalOnMissingBean
    public ExecutorService virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }

    @Bean
    @ConditionalOnMissingBean
    public UnlockMessageListenerManager unlockMessageListenerManager(
            RedisMessageListenerContainer listenerContainer,
            Executor redisLockMessageExecutor) {
        return new UnlockMessageListenerManager(listenerContainer, redisLockMessageExecutor);
    }

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean
    public LockWatchdog lockWatchdog(RedisLockOperations ops, RedisLockErrorHandler handler, RedisLockProperties properties) {
        return new LockWatchdog(ops, handler, properties.getWatchdog());
    }

    @Bean
    @ConditionalOnMissingBean
    public LockComponentRegistry lockComponentRegistry(
            ScriptLoader scriptLoader,
            UnlockMessageListenerManager unlockMessageListenerManager,
            ObjectProvider<LockWatchdog> lockWatchdog,
            RedisLockOperations redisLockOperations,
            DefaultLockOwnerSupplier defaultLockOwnerSupplier,
            RedisLockProperties redisLockProperties,
            RedisLockErrorHandler redisLockErrorHandler,
            ObjectProvider<LockMonitor> lockMonitorProvider) {
        return new LockComponentRegistry(scriptLoader, unlockMessageListenerManager, lockWatchdog.getIfAvailable(),
                redisLockOperations, defaultLockOwnerSupplier, redisLockProperties, redisLockErrorHandler,
                lockMonitorProvider);
    }

    @Bean
    @ConditionalOnMissingBean
    public LockBucketRegistry lockBucketRegistry(LockComponentRegistry componentRegistry,
            RedisLockProperties globalProperties) {
        return new LockBucketRegistry(componentRegistry, globalProperties);
    }
}